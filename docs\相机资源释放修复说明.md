# 相机资源释放修复说明

## 问题描述
关闭窗口后相机没有正确释放，导致下次打开相机失败。这是因为海康威视相机的SDK资源没有被正确清理。

## 修复措施

### 1. 修改 MutiCamApp 析构函数
**文件**: `src/MutiCamApp.cpp`

**改进内容**:
- 添加详细的调试日志输出
- 确保在析构时先停止所有相机采集
- 断开所有相机连接
- 使用 `reset()` 方法显式释放相机管理器
- 添加析构完成的确认日志

```cpp
MutiCamApp::~MutiCamApp()
{
    qDebug() << "MutiCamApp destructor called";
    
    // 停止测量
    if (m_isMeasuring) {
        onStopMeasureClicked();
    }
    
    // 清理相机管理器 - 确保完全释放资源
    if (m_cameraManager) {
        qDebug() << "Cleaning up camera manager...";
        
        // 停止所有相机采集
        m_cameraManager->stopAllCameras();
        
        // 断开所有相机连接
        m_cameraManager->disconnectAllCameras();
        
        // 重置相机管理器指针，触发其析构函数
        m_cameraManager.reset();
        
        qDebug() << "Camera manager cleaned up";
    }
    
    delete ui; 
    
    qDebug() << "MutiCamApp destructor completed";
}
```

### 2. 改进 CameraManager 析构函数
**文件**: `src/core/camera/camera_manager.cpp`

**改进内容**:
- 先调用 `stopAllCameras()` 停止所有相机采集
- 为每个相机线程添加详细的停止日志
- 增加线程等待超时时间（5秒）
- 如果线程无法正常结束，强制终止并再等待1秒
- 确保所有线程都正确结束后再清理资源

```cpp
CameraManager::~CameraManager() {
    qDebug() << "CameraManager destroying...";
    
    // 首先停止所有相机采集
    stopAllCameras();
    
    // 停止并等待所有相机线程结束
    for (auto& pair : m_cameraThreads) {
        if (pair.second) {
            qDebug() << "Stopping camera thread:" << QString::fromStdString(pair.first);
            
            // 停止采集
            pair.second->stopCapture();
            
            // 退出线程
            pair.second->quit();
            
            // 等待线程结束，最多等待5秒
            if (!pair.second->wait(5000)) {
                qWarning() << "Camera thread" << QString::fromStdString(pair.first) << "did not finish within timeout, terminating...";
                pair.second->terminate();
                pair.second->wait(1000); // 再等待1秒
            }
            
            qDebug() << "Camera thread stopped:" << QString::fromStdString(pair.first);
        }
    }
    
    // 断开所有相机连接
    disconnectAllCameras();
    
    // 清理资源
    m_cameraThreads.clear();
    m_cameras.clear();
    
    qDebug() << "CameraManager destroyed";
}
```

### 3. 修复 HikvisionCamera 的 disconnect 方法
**文件**: `src/core/camera/hikvision_camera.cpp`

**改进内容**:
- 添加互斥锁保护
- 添加详细的调试日志
- 确保在断开连接时调用 `closeDevice()` 方法
- `closeDevice()` 方法会正确调用海康威视SDK的清理函数：
  - `MV_CC_CloseDevice()` - 关闭设备
  - `MV_CC_DestroyHandle()` - 销毁句柄
  - `MV_CC_Finalize()` - 清理SDK

```cpp
bool HikvisionCamera::disconnect() {
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "Disconnecting camera:" << m_params.serialNumber.c_str();
    
    // 停止采集
    if (m_state == CameraState::Streaming) {
        stopStreaming();
    }
    
    // 关闭设备并释放资源
    if (m_state != CameraState::Disconnected) {
        closeDevice();
    }
    
    m_state = CameraState::Disconnected;
    emit stateChanged(m_state);
    
    qDebug() << "Camera disconnected:" << m_params.serialNumber.c_str();
    return true;
}
```

## 关键改进点

### 1. 资源释放顺序
1. 停止测量操作
2. 停止所有相机采集
3. 停止并等待所有相机线程结束
4. 断开所有相机连接（调用SDK清理函数）
5. 清理内存中的对象

### 2. 线程安全
- 使用互斥锁保护关键操作
- 确保线程正确结束后再释放资源
- 添加超时机制防止死锁

### 3. 错误处理
- 添加详细的调试日志便于问题排查
- 线程无法正常结束时强制终止
- 每个步骤都有相应的错误处理

### 4. SDK资源清理
- 确保调用海康威视SDK的所有清理函数
- 按正确顺序释放SDK资源
- 避免资源泄漏

## 测试建议

1. **正常关闭测试**: 启动应用 → 开始测量 → 停止测量 → 关闭应用 → 重新启动应用
2. **异常关闭测试**: 启动应用 → 开始测量 → 直接关闭应用 → 重新启动应用
3. **多次重启测试**: 重复上述操作多次，确保每次都能正常启动相机
4. **日志检查**: 查看控制台输出，确认所有资源都被正确释放

## 预期效果

修复后，应用程序关闭时会：
1. 正确停止所有相机采集
2. 等待所有线程安全结束
3. 释放所有海康威视SDK资源
4. 下次启动时能够正常连接和使用相机

通过这些改进，相机资源释放问题应该得到彻底解决。