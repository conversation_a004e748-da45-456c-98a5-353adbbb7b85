<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MutiCamApp</class>
 <widget class="QMainWindow" name="MutiCamApp">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1014</width>
    <height>739</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>512</width>
    <height>400</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>靶装配对接测量软件</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QTabWidget" name="tabWidget">
      <property name="font">
       <font>
        <pointsize>9</pointsize>
        <bold>true</bold>
       </font>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tabMain">
       <attribute name="title">
        <string>主界面</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_main" stretch="1,1">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_top" stretch="1,1">
          <item>
           <widget class="QGroupBox" name="groupBoxVertical">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>垂直视图</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_vertical">
             <property name="sizeConstraint">
              <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="lbVerticalView">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignCenter</set>
               </property>
              </widget>
             </item>

            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxLeft">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>左侧视图</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_left">
             <property name="sizeConstraint">
              <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="lbLeftView">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignCenter</set>
               </property>
              </widget>
             </item>

            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_bottom" stretch="1,1">
          <item>
           <widget class="QGroupBox" name="groupBoxFront">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>对向视图</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_front">
             <property name="sizeConstraint">
              <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="lbFrontView">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignCenter</set>
               </property>
              </widget>
             </item>

            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxControl">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="acceptDrops">
             <bool>false</bool>
            </property>
            <property name="title">
             <string>控制面板</string>
            </property>
            <property name="flat">
             <bool>false</bool>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_control">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_gridAndDetection">
               <item>
                <widget class="QGroupBox" name="groupBoxDrawTools">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="title">
                  <string>绘画</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignCenter</set>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_drawTools">
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_drawRow1">
                    <item>
                     <widget class="QPushButton" name="btnDrawPoint">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>点</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="btnDrawStraight">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>直线</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="btnDrawSimpleCircle">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>简单圆</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_drawRow2">
                    <item>
                     <widget class="QPushButton" name="btnDrawParallel">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>平行线</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="btnDraw2Line">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>线与线</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="btnDrawFineCircle">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>精细圆</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnCan1StepDraw">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>撤销上步绘画</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnClearDrawings">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>清空绘画</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnSaveImage">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>保存图像（原始+可视化）</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBoxGrid">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="title">
                  <string>网格</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_gridControl">
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_gridDensity">
                    <item>
                     <widget class="QLabel" name="labelGridDensity">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>网格密度</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QLineEdit" name="leGridDensity">
                      <property name="minimumSize">
                       <size>
                        <width>0</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>60</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="text">
                       <string/>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignmentFlag::AlignCenter</set>
                      </property>
                      <property name="placeholderText">
                       <string>0</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QLabel" name="labelPixel">
                      <property name="text">
                       <string>像素</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="btnCancelGrids">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>取消网格</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <widget class="QGroupBox" name="groupBoxAutoDetection">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="title">
                     <string>自动测量</string>
                    </property>
                    <layout class="QHBoxLayout" name="horizontalLayout">
                     <item>
                      <widget class="QPushButton" name="btnCircleDet">
                       <property name="font">
                        <font>
                         <pointsize>9</pointsize>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="text">
                        <string>圆查找</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="btnLineDet">
                       <property name="font">
                        <font>
                         <pointsize>9</pointsize>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="text">
                        <string>直线查找</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="btnCan1StepDet">
                       <property name="font">
                        <font>
                         <pointsize>9</pointsize>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="text">
                        <string>撤销上步</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </widget>
                  </item>
                  <item>
                   <widget class="QGroupBox" name="groupBoxMeasurement">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="title">
                     <string>运行</string>
                    </property>
                    <layout class="QHBoxLayout" name="horizontalLayout_measureButtons">
                     <item>
                      <widget class="QPushButton" name="btnStartMeasure">
                       <property name="enabled">
                        <bool>true</bool>
                       </property>
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>0</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>9</pointsize>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="contextMenuPolicy">
                        <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
                       </property>
                       <property name="styleSheet">
                        <string>QPushButton { background-color: #4CAF50; color: white; border: none; border-radius: 5px; }
QPushButton:hover { background-color: #45a049; }
QPushButton:pressed { background-color: #3d8b40; }</string>
                       </property>
                       <property name="text">
                        <string>开始测量</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="btnStopMeasure">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>0</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>9</pointsize>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string>QPushButton { background-color: #f44336; color: white; border: none; border-radius: 5px; }
QPushButton:hover { background-color: #da190b; }
QPushButton:pressed { background-color: #b71c1c; }</string>
                       </property>
                       <property name="text">
                        <string>停止测量</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabVertical">
       <attribute name="title">
        <string>垂直视图</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_tabVertical">
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBoxVerticalMain">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="acceptDrops">
           <bool>false</bool>
          </property>
          <property name="title">
           <string/>
          </property>
          <property name="flat">
           <bool>false</bool>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_verticalMain" stretch="0,1,3">
           <item>
            <widget class="QGroupBox" name="groupBoxVerticalGrid">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>网格</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
             </property>
             <layout class="QGridLayout" name="gridLayout_verticalGrid">
              <item row="0" column="0" colspan="2">
               <widget class="QLabel" name="labelVerticalGridDensity">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>输入网格密度</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLineEdit" name="leGridDensVertical">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
                <property name="placeholderText">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QPushButton" name="btnCancelGridsVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消网格</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLabel" name="labelVerticalPixel">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>像素</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxVerticalViewControl">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>视图控制</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_verticalViewControl">
              <item>
               <widget class="QPushButton" name="btnResetZoomVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>重置缩放</string>
                </property>
                <property name="toolTip">
                 <string>重置视图缩放和平移到默认状态</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="labelZoomInfoVertical">
                <property name="font">
                 <font>
                  <pointsize>8</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>缩放: 100%</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxVerticalAutoMeasure">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>自动测量</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_verticalAutoMeasure">
              <item>
               <widget class="QPushButton" name="btnLineDetVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCircleDetVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>圆查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDetVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销测量</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxVerticalDrawTools">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>绘图工具</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_verticalDrawTools">
              <item>
               <widget class="QPushButton" name="btnDrawPointVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>点</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawStraightVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawSimpleCircleVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>简单圆</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawFineCircleVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>精确圆</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawParallelVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>平行线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDraw2LineVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>角度线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDrawVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销绘画</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnClearDrawingsVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>清空绘画</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCalibrationVertical">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>标定</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QGroupBox" name="groupBoxVerticalDisplay">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>图像显示</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_verticalDisplay">
           <item>
            <widget class="QLabel" name="lbVerticalView2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btnSaveImageVertical">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>保存图像</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_verticalTemplate2">
             <item>
              <widget class="QPushButton" name="btnLoadTemplateVertical2">
               <property name="maximumSize">
                <size>
                 <width>80</width>
                 <height>25</height>
                </size>
               </property>
               <property name="text">
                <string>加载模版</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnStartMatchingVertical2">
               <property name="maximumSize">
                <size>
                 <width>80</width>
                 <height>25</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>background-color: green; color: white;</string>
               </property>
               <property name="text">
                <string>开始匹配</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_verticalTemplate2">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabLeft">
       <attribute name="title">
        <string>左侧视图</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_tabLeft">
        <item>
         <widget class="QGroupBox" name="groupBoxLeftMain">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_leftMain" stretch="0,1,3">
           <item>
            <widget class="QGroupBox" name="groupBoxLeftGrid">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>网格</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_leftGrid">
              <item row="1" column="0">
               <widget class="QLineEdit" name="leGridDensLeft">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
                <property name="placeholderText">
                 <string>0</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0" colspan="2">
               <widget class="QLabel" name="labelLeftGridDensity">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>输入网格密度</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLabel" name="labelLeftPixel">
                <property name="text">
                 <string>像素</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QPushButton" name="btnCancelGridsLeft">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消网格</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxLeftViewControl">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>视图控制</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_leftViewControl">
              <item>
               <widget class="QPushButton" name="btnResetZoomLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>重置缩放</string>
                </property>
                <property name="toolTip">
                 <string>重置视图缩放和平移到默认状态</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="labelZoomInfoLeft">
                <property name="font">
                 <font>
                  <pointsize>8</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>缩放: 100%</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxLeftAutoMeasure">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>自动测量</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_leftAutoMeasure">
              <item>
               <widget class="QPushButton" name="btnLineDetLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCircleDetLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>圆查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDetLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销测量</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxLeftDrawTools">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>绘图工具</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_leftDrawTools">
              <item>
               <widget class="QPushButton" name="btnDrawPointLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>点</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawStraightLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawSimpleCircleLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>简单圆</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawFineCircleLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>精确圆</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawParallelLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>平行线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDraw2LineLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>角度线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDrawLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销绘画</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnClearDrawingsLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>清空绘画</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCalibrationLeft">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>标定</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxLeftDisplay">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>图像显示</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_leftDisplay">
           <item>
            <widget class="QLabel" name="lbLeftView2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btnSaveImageLeft">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>保存图像</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_leftTemplate2">
             <item>
              <widget class="QPushButton" name="btnLoadTemplateLeft2">
               <property name="maximumSize">
                <size>
                 <width>80</width>
                 <height>25</height>
                </size>
               </property>
               <property name="text">
                <string>加载模版</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnStartMatchingLeft2">
               <property name="maximumSize">
                <size>
                 <width>80</width>
                 <height>25</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>background-color: green; color: white;</string>
               </property>
               <property name="text">
                <string>开始匹配</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_leftTemplate2">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabFront">
       <attribute name="title">
        <string>对向视图</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_tabFront">
        <item>
         <widget class="QGroupBox" name="groupBoxFrontMain">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_frontMain" stretch="0,1,3">
           <item>
            <widget class="QGroupBox" name="groupBoxFrontGrid">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>网格</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_frontGrid">
              <item row="2" column="0" colspan="2">
               <widget class="QPushButton" name="btnCancelGridsFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消网格</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLabel" name="labelFrontPixel">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>像素</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0" colspan="2">
               <widget class="QLabel" name="labelFrontGridDensity">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>输入网格密度</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLineEdit" name="leGridDensFront">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
                <property name="placeholderText">
                 <string>0</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxFrontViewControl">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>视图控制</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_frontViewControl">
              <item>
               <widget class="QPushButton" name="btnResetZoomFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>重置缩放</string>
                </property>
                <property name="toolTip">
                 <string>重置视图缩放和平移到默认状态</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="labelZoomInfoFront">
                <property name="font">
                 <font>
                  <pointsize>8</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>缩放: 100%</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxFrontAutoMeasure">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>自动测量</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_frontAutoMeasure">
              <item>
               <widget class="QPushButton" name="btnLineDetFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCircleDetFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>圆查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDetFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销测量</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBoxFrontDrawTools">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>绘图工具</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_frontDrawTools">
              <item>
               <widget class="QPushButton" name="btnDrawPointFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>点</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawStraightFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawSimpleCircleFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>简单圆</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawFineCircleFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>精确圆</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDrawParallelFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>平行线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnDraw2LineFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>角度线</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDrawFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销绘画</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnClearDrawingsFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>清空绘画</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCalibrationFront">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>标定</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxFrontDisplay">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>图像显示</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_frontDisplay">
           <item>
            <widget class="QLabel" name="lbFrontView2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btnSaveImageFront">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>保存图像</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_frontTemplate2">
             <item>
              <widget class="QPushButton" name="btnLoadTemplateFront2">
               <property name="maximumSize">
                <size>
                 <width>80</width>
                 <height>25</height>
                </size>
               </property>
               <property name="text">
                <string>加载模版</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnStartMatchingFront2">
               <property name="maximumSize">
                <size>
                 <width>80</width>
                 <height>25</height>
                </size>
               </property>
               <property name="styleSheet">
                <string>background-color: green; color: white;</string>
               </property>
               <property name="text">
                <string>开始匹配</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_frontTemplate2">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabSettings">
       <attribute name="title">
        <string>参数设置</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_settings">
        <item row="0" column="0">
         <layout class="QGridLayout" name="gridLayout_settingsMain">
          <item row="0" column="0">
           <widget class="QGroupBox" name="groupBoxDisplaySettings">
            <property name="title">
             <string>显示设置</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_display">
             <item row="1" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_uiWidth" stretch="2,0,1,3">
               <item>
                <widget class="QLabel" name="labelUIWidth">
                 <property name="text">
                  <string>界面总宽:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="ledUIWidth"/>
               </item>
               <item>
                <widget class="QLabel" name="labelUIWidthUnit">
                 <property name="text">
                  <string>像素</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_uiWidth">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item row="2" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_uiHeight" stretch="2,0,1,3">
               <item>
                <widget class="QLabel" name="labelUIHeight">
                 <property name="text">
                  <string>界面总高:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="ledUIHeight"/>
               </item>
               <item>
                <widget class="QLabel" name="labelUIHeightUnit">
                 <property name="text">
                  <string>像素</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_uiHeight">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item row="3" column="0">
              <spacer name="verticalSpacer_display">
               <property name="orientation">
                <enum>Qt::Orientation::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <widget class="QGroupBox" name="groupBoxCameraSettings">
          <property name="title">
           <string>相机采集参数</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_camera">
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_verCamSN" stretch="4,4,4,0">
             <item>
              <widget class="QLabel" name="labelVerCamSN">
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>垂直相机SN码：</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="ledVerCamSN"/>
             </item>
             <item>
              <spacer name="horizontalSpacer_verCamSN">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="labelVerCamSNStatus">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_leftCamSN" stretch="4,4,4,0">
             <item>
              <widget class="QLabel" name="labelLeftCamSN">
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>左侧相机SN码：</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="ledLeftCamSN"/>
             </item>
             <item>
              <spacer name="horizontalSpacer_leftCamSN">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="labelLeftCamSNStatus">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="2" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_frontCamSN" stretch="4,4,4,0">
             <item>
              <widget class="QLabel" name="labelFrontCamSN">
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>对向相机SN码：</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="ledFrontCamSN"/>
             </item>
             <item>
              <spacer name="horizontalSpacer_frontCamSN">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="labelFrontCamSNStatus">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="3" column="0">
            <spacer name="verticalSpacer_camera">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1" rowspan="2">
         <widget class="QGroupBox" name="groupBoxAutoMeasureSettings">
          <property name="title">
           <string>自动测量参数</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_autoMeasure">
           <item row="0" column="0">
            <widget class="QGroupBox" name="groupBoxLineDetection">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>直线查找参数</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_lineDetection">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_cannyLineLow" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelCannyLineLow">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘低阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyLineLow">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_cannyLineLow">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_cannyLineHigh" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelCannyLineHigh">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘高阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyLineHigh">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_cannyLineHigh">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_lineDetThreshold" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelLineDetThreshold">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>直线查找阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledLineDetThreshold">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_lineDetThreshold">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_lineDetMinLength" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelLineDetMinLength">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>最小线长</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledLineDetMinLength">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_lineDetMinLength">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_lineDetMaxGap" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelLineDetMaxGap">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>最大间隙</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledLineDetMaxGap">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_lineDetMaxGap">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QGroupBox" name="groupBoxCircleDetection">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>圆查找参数</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_circleDetection">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_cannyCircleLow" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelCannyCircleLow">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘低阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyCircleLow">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_cannyCircleLow">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_cannyCircleHigh" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelCannyCircleHigh">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘高阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyCircleHigh">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_cannyCircleHigh">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_circleDetParam2" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="labelCircleDetParam2">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>圆查找阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCircleDetParam2">
                  <property name="font">
                   <font>
                    <pointsize>9</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_circleDetParam2">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabCameraStatus">
       <attribute name="title">
        <string>相机状态监控</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_cameraStatus">
        <item>
         <widget class="QGroupBox" name="groupBoxCameraOverview">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>相机状态总览</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_cameraOverview">
           <item>
            <widget class="QLabel" name="labelTotalCameras">
             <property name="font">
              <font>
               <pointsize>10</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>总相机数: 3</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="labelOnlineCameras">
             <property name="font">
              <font>
               <pointsize>10</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string>color: green;</string>
             </property>
             <property name="text">
              <string>在线: 0</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="labelOfflineCameras">
             <property name="font">
              <font>
               <pointsize>10</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string>color: red;</string>
             </property>
             <property name="text">
              <string>离线: 3</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_cameraOverview">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_cameraDetails">
          <item>
           <widget class="QGroupBox" name="groupBoxVerticalCameraStatus">
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>垂直相机</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_verticalCamera">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_verticalStatus">
               <item>
                <widget class="QLabel" name="labelVerticalStatusTitle">
                 <property name="text">
                  <string>连接状态:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelVerticalStatus">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: red;</string>
                 </property>
                 <property name="text">
                  <string>离线</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_verticalStatus">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_verticalFPS">
               <item>
                <widget class="QLabel" name="labelVerticalFPSTitle">
                 <property name="text">
                  <string>帧率:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelVerticalFPS">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>-- fps</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_verticalFPS">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_verticalResolution">
               <item>
                <widget class="QLabel" name="labelVerticalResolutionTitle">
                 <property name="text">
                  <string>分辨率:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelVerticalResolution">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>--</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_verticalResolution">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_verticalExposure">
               <item>
                <widget class="QLabel" name="labelVerticalExposureTitle">
                 <property name="text">
                  <string>曝光时间:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelVerticalExposure">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>--ms</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_verticalExposure">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_verticalGain">
               <item>
                <widget class="QLabel" name="labelVerticalGainTitle">
                 <property name="text">
                  <string>增益:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelVerticalGain">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>--dB</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_verticalGain">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxLeftCameraStatus">
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>左侧相机</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_leftCamera">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_leftStatus">
               <item>
                <widget class="QLabel" name="labelLeftStatusTitle">
                 <property name="text">
                  <string>连接状态:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelLeftStatus">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: red;</string>
                 </property>
                 <property name="text">
                  <string>离线</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_leftStatus">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_leftFPS">
               <item>
                <widget class="QLabel" name="labelLeftFPSTitle">
                 <property name="text">
                  <string>帧率:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelLeftFPS">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>-- fps</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_leftFPS">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_leftResolution">
               <item>
                <widget class="QLabel" name="labelLeftResolutionTitle">
                 <property name="text">
                  <string>分辨率:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelLeftResolution">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>--</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_leftResolution">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_leftExposure">
               <item>
                <widget class="QLabel" name="labelLeftExposureTitle">
                 <property name="text">
                  <string>曝光时间:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelLeftExposure">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>--ms</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_leftExposure">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_leftGain">
               <item>
                <widget class="QLabel" name="labelLeftGainTitle">
                 <property name="text">
                  <string>增益:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelLeftGain">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>--dB</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_leftGain">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxFrontCameraStatus">
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>对向相机</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_frontCamera">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_frontStatus">
               <item>
                <widget class="QLabel" name="labelFrontStatusTitle">
                 <property name="text">
                  <string>连接状态:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelFrontStatus">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string>color: red;</string>
                 </property>
                 <property name="text">
                  <string>离线</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_frontStatus">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_frontFPS">
               <item>
                <widget class="QLabel" name="labelFrontFPSTitle">
                 <property name="text">
                  <string>帧率:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelFrontFPS">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>-- fps</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_frontFPS">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_frontResolution">
               <item>
                <widget class="QLabel" name="labelFrontResolutionTitle">
                 <property name="text">
                  <string>分辨率:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelFrontResolution">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>--</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_frontResolution">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_frontExposure">
               <item>
                <widget class="QLabel" name="labelFrontExposureTitle">
                 <property name="text">
                  <string>曝光时间:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelFrontExposure">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>--ms</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_frontExposure">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_frontGain">
               <item>
                <widget class="QLabel" name="labelFrontGainTitle">
                 <property name="text">
                  <string>增益:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="labelFrontGain">
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>--dB</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_frontGain">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxCameraAlerts">
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>故障诊断与报警</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_cameraAlerts">
           <item>
            <widget class="QTextEdit" name="textEditAlerts">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>120</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <bold>false</bold>
              </font>
             </property>
             <property name="html">
              <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;[警告] 对向相机连接丢失 - 2025-01-15 14:32:15&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#ff8000;&quot;&gt;[提醒] 垂直相机温度偏高(42.5°C) - 2025-01-15 14:30:22&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#008000;&quot;&gt;[信息] 左侧相机连接正常 - 2025-01-15 14:28:45&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#008000;&quot;&gt;[信息] 垂直相机连接正常 - 2025-01-15 14:28:43&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_alertButtons">
             <item>
              <widget class="QPushButton" name="btnClearAlerts">
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>清空报警</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnRefreshStatus">
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>刷新状态</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_alertButtons">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_cameraStatus">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabStageControl">
       <attribute name="title">
        <string>XYZ载物台控制</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_stageControl">
        <item>
         <widget class="QGroupBox" name="groupBoxStagePosition">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>当前位置</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_stagePosition">
           <item row="0" column="0">
            <widget class="QLabel" name="labelXPosition">
             <property name="text">
              <string>X轴位置:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="labelXPositionValue">
             <property name="styleSheet">
              <string>color: blue; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>125.456 mm</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelYPosition">
             <property name="text">
              <string>Y轴位置:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="labelYPositionValue">
             <property name="styleSheet">
              <string>color: blue; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>89.234 mm</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="labelZPosition">
             <property name="text">
              <string>Z轴位置:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLabel" name="labelZPositionValue">
             <property name="styleSheet">
              <string>color: blue; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>67.890 mm</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxStageControl">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>载物台控制</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_stageControl">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_stepSize">
             <item>
              <widget class="QLabel" name="labelStepSize">
               <property name="text">
                <string>移动步长:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QRadioButton" name="radioStep01">
               <property name="text">
                <string>0.1mm</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QRadioButton" name="radioStep1">
               <property name="text">
                <string>1mm</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QRadioButton" name="radioStep10">
               <property name="text">
                <string>10mm</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_stepSize">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_xyControl">
             <item>
              <widget class="QGroupBox" name="groupBoxXYControl">
               <property name="title">
                <string>X-Y轴控制</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_xyControl">
                <item row="0" column="1">
                 <widget class="QPushButton" name="btnMoveYUp">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>Y+</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QPushButton" name="btnMoveXLeft">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>X-</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QLabel" name="labelXYCenter">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                  <property name="text">
                   <string>X-Y</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="2">
                 <widget class="QPushButton" name="btnMoveXRight">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>X+</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="1">
                 <widget class="QPushButton" name="btnMoveYDown">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>Y-</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBoxZControl">
               <property name="title">
                <string>Z轴控制</string>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_zControl">
                <item>
                 <widget class="QPushButton" name="btnMoveZUp">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>Z+</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="labelZCenter">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                  <property name="text">
                   <string>Z轴</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btnMoveZDown">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>40</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>Z-</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_stageButtons">
             <item>
              <widget class="QPushButton" name="btnStageHome">
               <property name="text">
                <string>回到原点</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnStageStop">
               <property name="styleSheet">
                <string>background-color: red; color: white;</string>
               </property>
               <property name="text">
                <string>紧急停止</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxStageStatus">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>载物台状态</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_stageStatus">
           <item row="0" column="0">
            <widget class="QLabel" name="labelStageConnectionTitle">
             <property name="text">
              <string>连接状态:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="labelStageConnection">
             <property name="styleSheet">
              <string>color: green; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>已连接</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelStageStatusTitle">
             <property name="text">
              <string>运动状态:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="labelStageMovingStatus">
             <property name="styleSheet">
              <string>color: blue;</string>
             </property>
             <property name="text">
              <string>静止</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="labelStageSpeedTitle">
             <property name="text">
              <string>移动速度:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLabel" name="labelStageSpeed">
             <property name="text">
              <string>10.0 mm/s</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxTrajectoryControl">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>轨迹记录</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_trajectoryControl">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_trajectoryButtons">
             <item>
              <widget class="QPushButton" name="btnTrajectoryStart">
               <property name="text">
                <string>开始记录</string>
               </property>
               <property name="styleSheet">
                <string>background-color: green; color: white;</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnTrajectoryStop">
               <property name="text">
                <string>停止记录</string>
               </property>
               <property name="styleSheet">
                <string>background-color: orange; color: white;</string>
               </property>
               <property name="enabled">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnTrajectoryClear">
               <property name="text">
                <string>清空轨迹</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnTrajectoryExport">
               <property name="text">
                <string>导出轨迹</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QGridLayout" name="gridLayout_trajectoryStats">
             <item row="0" column="0">
              <widget class="QLabel" name="labelTrajectoryPointsTitle">
               <property name="text">
                <string>记录点数:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="labelTrajectoryPoints">
               <property name="styleSheet">
                <string>color: blue; font-weight: bold;</string>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="labelTrajectoryDistanceTitle">
               <property name="text">
                <string>总距离:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="labelTrajectoryDistance">
               <property name="styleSheet">
                <string>color: blue; font-weight: bold;</string>
               </property>
               <property name="text">
                <string>0.0 mm</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="labelTrajectoryStatusTitle">
               <property name="text">
                <string>记录状态:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="labelTrajectoryStatus">
               <property name="styleSheet">
                <string>color: gray; font-weight: bold;</string>
               </property>
               <property name="text">
                <string>未记录</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_stageControl">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabPhysicalControl">
       <attribute name="title">
        <string>物理按键控制</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_physicalControl">
        <item>
         <widget class="QGroupBox" name="groupBoxKeyStatus">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>按键状态监控</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_keyStatus">
           <item row="0" column="0">
            <widget class="QLabel" name="labelKey1Title">
             <property name="text">
              <string>启动按键:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="labelKey1Status">
             <property name="styleSheet">
              <string>color: gray;</string>
             </property>
             <property name="text">
              <string>未按下</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelKey2Title">
             <property name="text">
              <string>停止按键:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="labelKey2Status">
             <property name="styleSheet">
              <string>color: gray;</string>
             </property>
             <property name="text">
              <string>未按下</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="labelKey3Title">
             <property name="text">
              <string>复位按键:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLabel" name="labelKey3Status">
             <property name="styleSheet">
              <string>color: gray;</string>
             </property>
             <property name="text">
              <string>未按下</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="labelKey4Title">
             <property name="text">
              <string>急停按键:</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLabel" name="labelKey4Status">
             <property name="styleSheet">
              <string>color: green; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>正常</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxKeyConfig">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>按键功能配置</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_keyConfig">
           <item row="0" column="0">
            <widget class="QLabel" name="labelKeyFunction1">
             <property name="text">
              <string>按键1功能:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QComboBox" name="comboBoxKey1Function">
             <item>
              <property name="text">
               <string>启动相机采集</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>开始检测</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>载物台回原点</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelKeyFunction2">
             <property name="text">
              <string>按键2功能:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QComboBox" name="comboBoxKey2Function">
             <item>
              <property name="text">
               <string>停止相机采集</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>暂停检测</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>载物台停止</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="labelKeyFunction3">
             <property name="text">
              <string>按键3功能:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QComboBox" name="comboBoxKey3Function">
             <item>
              <property name="text">
               <string>系统复位</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>清除报警</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>保存图像</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxKeyTest">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>按键测试</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_keyTest">
           <item>
            <widget class="QPushButton" name="btnTestKey1">
             <property name="text">
              <string>测试按键1</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btnTestKey2">
             <property name="text">
              <string>测试按键2</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btnTestKey3">
             <property name="text">
              <string>测试按键3</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btnTestEmergencyStop">
             <property name="styleSheet">
              <string>background-color: orange; color: white;</string>
             </property>
             <property name="text">
              <string>测试急停</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_physicalControl">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabGratingPosition">
       <attribute name="title">
        <string>光栅尺位置显示</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_gratingPosition">
        <item>
         <widget class="QGroupBox" name="groupBoxGratingStatus">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>光栅尺状态</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_gratingStatus">
           <item row="0" column="0">
            <widget class="QLabel" name="labelGratingXTitle">
             <property name="text">
              <string>X轴光栅尺:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="labelGratingXStatus">
             <property name="styleSheet">
              <string>color: green; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>正常</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelGratingYTitle">
             <property name="text">
              <string>Y轴光栅尺:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="labelGratingYStatus">
             <property name="styleSheet">
              <string>color: green; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>正常</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="labelGratingZTitle">
             <property name="text">
              <string>Z轴光栅尺:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLabel" name="labelGratingZStatus">
             <property name="styleSheet">
              <string>color: green; font-weight: bold;</string>
             </property>
             <property name="text">
              <string>正常</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxGratingPosition">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>实时位置读数</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_gratingPosition">
           <item row="0" column="0">
            <widget class="QLabel" name="labelGratingXPosTitle">
             <property name="text">
              <string>X轴位置:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="labelGratingXPosition">
             <property name="styleSheet">
              <string>color: blue; font-weight: bold; font-size: 14px;</string>
             </property>
             <property name="text">
              <string>125.4567 mm</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelGratingYPosTitle">
             <property name="text">
              <string>Y轴位置:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="labelGratingYPosition">
             <property name="styleSheet">
              <string>color: blue; font-weight: bold; font-size: 14px;</string>
             </property>
             <property name="text">
              <string>89.2345 mm</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="labelGratingZPosTitle">
             <property name="text">
              <string>Z轴位置:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLabel" name="labelGratingZPosition">
             <property name="styleSheet">
              <string>color: blue; font-weight: bold; font-size: 14px;</string>
             </property>
             <property name="text">
              <string>67.8901 mm</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxGratingCalibration">
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string>光栅尺校准</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_gratingCalibration">
           <item row="0" column="0">
            <widget class="QLabel" name="labelGratingResolutionTitle">
             <property name="text">
              <string>分辨率:</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="labelGratingResolution">
             <property name="text">
              <string>0.001 mm</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="labelGratingAccuracyTitle">
             <property name="text">
              <string>精度:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="labelGratingAccuracy">
             <property name="text">
              <string>±0.002 mm</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0" colspan="2">
            <layout class="QHBoxLayout" name="horizontalLayout_gratingButtons">
             <item>
              <widget class="QPushButton" name="btnGratingZero">
               <property name="text">
                <string>设置零点</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnGratingCalibrate">
               <property name="text">
                <string>校准光栅尺</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnGratingReset">
               <property name="text">
                <string>复位</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_gratingPosition">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1014</width>
     <height>33</height>
    </rect>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
